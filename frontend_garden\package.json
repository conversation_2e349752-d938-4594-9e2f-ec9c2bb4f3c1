{"name": "frontend_garden", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.15", "@testing-library/dom": "^9.3.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.8", "i18next": "^23.11.2", "i18next-browser-languagedetector": "^7.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.1.0", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-toastify": "^10.0.5", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "build:prod": "CI=false npm run build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"typescript": "^4.9.5"}, "resolutions": {"typescript": "^4.9.5"}}